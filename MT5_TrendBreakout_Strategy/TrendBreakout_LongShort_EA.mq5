//+------------------------------------------------------------------+
//|                                    TrendBreakout_LongShort_EA.mq5 |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "1.00"

#include <Trade\Trade.mqh>

// 交易对象
CTrade trade;

// ================================
// 输入参数
// ================================

//--- 指标参数 ---
input group "========== 指标参数 =========="
input int    EMA20_Period = 20;           // EMA20周期
input int    EMA50_Period = 50;           // EMA50周期
input int    Volume_MA_Period = 20;       // 成交量均线周期
input int    ATR_Period = 14;             // ATR周期

//--- 风险管理参数 ---
input group "========== 风险管理 =========="
input double Risk_Percent = 2.0;          // 风险比例(%)
input double Profit_Ratio = 2.0;          // 盈亏比
input bool   Enable_Trailing_Stop = true; // 启用追踪止损
input double Trailing_Start_Points = 200; // 追踪止损启动点数
input double Trailing_Stop_Points = 100;  // 追踪止损距离点数
input double Trailing_Step_Points = 50;   // 追踪止损步长点数
input bool   Enable_Breakeven = true;     // 启用保本功能
input double Breakeven_Trigger_Points = 150; // 保本触发距离点数
input double Breakeven_Offset_Points = 10;   // 保本偏移点数(成本线上方)

//--- 交易过滤参数 ---
input group "========== 交易过滤 =========="
input bool   Enable_MA_Filter = true;     // 启用均线过滤
input ENUM_TIMEFRAMES MA_Filter_Timeframe = PERIOD_H1; // 均线过滤时间周期
input int    MA_Filter_Period = 20;       // 均线过滤周期
input ENUM_MA_METHOD MA_Filter_Method = MODE_EMA; // 均线计算方法

//--- 时间过滤参数 ---
input group "========== 时间过滤 =========="
input bool   Enable_Time_Filter = false;  // 启用时间过滤
input bool   Trade_Monday = true;         // 周一交易
input bool   Trade_Tuesday = true;        // 周二交易
input bool   Trade_Wednesday = true;      // 周三交易
input bool   Trade_Thursday = true;       // 周四交易
input bool   Trade_Friday = true;         // 周五交易
input bool   Trade_Saturday = false;      // 周六交易
input bool   Trade_Sunday = false;        // 周日交易
input string Start_Time = "00:00";        // 开始交易时间
input string End_Time = "23:59";          // 结束交易时间

//--- 仓位管理参数 ---
input group "========== 仓位管理 =========="
input int    Max_Long_Positions = 3;      // 最大多头持仓数
input int    Max_Short_Positions = 3;     // 最大空头持仓数
input int    Magic_Number = 123456;       // 魔术数字

//--- 系统参数 ---
input group "========== 系统设置 =========="
input bool   Debug_Mode = true;           // 调试模式

// ================================
// 全局变量
// ================================
// 指标句柄
int ema20_handle, ema50_handle, atr_handle;
int ma_filter_handle;  // 均线过滤句柄

// 指标数组
double ema20[], ema50[], atr_values[];
double ma_filter_values[];  // 均线过滤数组

// 追踪止损管理 - 使用并行数组（支持多头和空头）
ulong trailing_tickets[];
double trailing_entry_prices[];
double trailing_current_stops[];
bool trailing_actives[];
ENUM_POSITION_TYPE trailing_types[];  // 新增：记录持仓类型

// 保本管理 - 使用并行数组
ulong breakeven_tickets[];
double breakeven_entry_prices[];
bool breakeven_applied[];
ENUM_POSITION_TYPE breakeven_types[];  // 新增：记录持仓类型

// 时间过滤变量
int start_time_seconds, end_time_seconds;

// 仪表盘监控变量
struct BreakoutMonitor {
    // 多头突破监控
    bool uptrend;
    bool long_volume_above_ma;
    bool long_price_above_highest;
    bool long_breakout_signal;
    double highest_high;
    double long_volume_ratio;
    
    // 空头突破监控
    bool downtrend;
    bool short_volume_above_ma;
    bool short_price_below_lowest;
    bool short_breakout_signal;
    double lowest_low;
    double short_volume_ratio;
} breakout_monitor;

struct PositionInfo {
    double total_long_lots;
    double total_short_lots;
    int total_long_positions;
    int total_short_positions;
    double floating_profit;
    double next_risk_amount;
    double account_balance;
    double account_equity;
} position_info;

// 交易统计结构
struct TradeStatistics {
    // 总体统计
    int total_trades;
    int winning_trades;
    int losing_trades;
    double win_rate;
    double net_profit;

    // 多头突破策略统计
    int long_breakout_total_trades;
    int long_breakout_winning_trades;
    int long_breakout_losing_trades;
    double long_breakout_net_profit;

    // 空头突破策略统计
    int short_breakout_total_trades;
    int short_breakout_winning_trades;
    int short_breakout_losing_trades;
    double short_breakout_net_profit;
} trade_stats;

// 仪表盘状态缓存，避免不必要的更新
struct DashboardCache {
    bool uptrend_last;
    bool downtrend_last;
    bool long_breakout_signal_last;
    bool short_breakout_signal_last;
    double highest_high_last;
    double lowest_low_last;
    double long_volume_ratio_last;
    double short_volume_ratio_last;
    int total_long_positions_last;
    int total_short_positions_last;
    double total_long_lots_last;
    double total_short_lots_last;
    double floating_profit_last;
    double next_risk_amount_last;

    // 交易统计缓存
    int total_trades_last;
    int winning_trades_last;
    int losing_trades_last;
    double win_rate_last;
    double net_profit_last;
    int long_breakout_total_trades_last;
    int long_breakout_winning_trades_last;
    int long_breakout_losing_trades_last;
    double long_breakout_net_profit_last;
    int short_breakout_total_trades_last;
    int short_breakout_winning_trades_last;
    int short_breakout_losing_trades_last;
    double short_breakout_net_profit_last;

    bool initialized;
} dashboard_cache;

// 记录上次交易信号时间，避免重复开单
datetime last_long_breakout_signal_time = 0;
datetime last_short_breakout_signal_time = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 设置交易品种为XAUUSD
    if(Symbol() != "XAUUSD") {
        Print("警告：此EA专为XAUUSD设计，当前品种：", Symbol());
    }
    
    // 检查时间框架
    if(Period() != PERIOD_M3) {
        Print("警告：此EA设计用于3分钟图表，当前周期：", EnumToString(Period()));
    }
    
    // 初始化指标
    ema20_handle = iMA(Symbol(), PERIOD_M3, EMA20_Period, 0, MODE_EMA, PRICE_CLOSE);
    ema50_handle = iMA(Symbol(), PERIOD_M3, EMA50_Period, 0, MODE_EMA, PRICE_CLOSE);
    atr_handle = iATR(Symbol(), PERIOD_M3, ATR_Period);

    // 初始化均线过滤指标
    if(Enable_MA_Filter) {
        ma_filter_handle = iMA(Symbol(), MA_Filter_Timeframe, MA_Filter_Period, 0, MA_Filter_Method, PRICE_CLOSE);
        if(ma_filter_handle == INVALID_HANDLE) {
            Print("均线过滤指标初始化失败");
            return INIT_FAILED;
        }
        ArraySetAsSeries(ma_filter_values, true);
    }

    // 检查指标句柄
    if(ema20_handle == INVALID_HANDLE || ema50_handle == INVALID_HANDLE ||
       atr_handle == INVALID_HANDLE) {
        Print("指标初始化失败");
        return INIT_FAILED;
    }

    // 设置数组为时间序列
    ArraySetAsSeries(ema20, true);
    ArraySetAsSeries(ema50, true);
    ArraySetAsSeries(atr_values, true);

    // 初始化时间过滤
    if(Enable_Time_Filter) {
        InitializeTimeFilter();
    }

    // 初始化追踪止损数组
    ArrayResize(trailing_tickets, 0);
    ArrayResize(trailing_entry_prices, 0);
    ArrayResize(trailing_current_stops, 0);
    ArrayResize(trailing_actives, 0);
    ArrayResize(trailing_types, 0);

    // 初始化保本管理数组
    ArrayResize(breakeven_tickets, 0);
    ArrayResize(breakeven_entry_prices, 0);
    ArrayResize(breakeven_applied, 0);
    ArrayResize(breakeven_types, 0);

    // 初始化交易统计
    InitializeTradeStatistics();

    // 设置魔术数字
    trade.SetExpertMagicNumber(Magic_Number);

    Print("趋势突破多空策略 初始化成功");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 释放指标句柄
    IndicatorRelease(ema20_handle);
    IndicatorRelease(ema50_handle);
    IndicatorRelease(atr_handle);

    // 释放均线过滤指标句柄
    if(Enable_MA_Filter && ma_filter_handle != INVALID_HANDLE) {
        IndicatorRelease(ma_filter_handle);
    }

    // 清除图表对象
    ObjectsDeleteAll(0, "Dashboard");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 获取指标数据
    if(!GetIndicatorData()) return;

    // 计算交易信号
    CalculateSignals();

    // 检查时间过滤
    if(Enable_Time_Filter && !IsTimeAllowed()) {
        UpdateDashboard();
        return;
    }

    // 管理追踪止损
    if(Enable_Trailing_Stop) {
        ManageTrailingStops();
    }

    // 管理保本功能（仅在未启用追踪止损时）
    if(!Enable_Trailing_Stop && Enable_Breakeven) {
        ManageBreakeven();
    }

    // 检查持仓数量
    int long_positions = CountLongPositions();
    int short_positions = CountShortPositions();

    // 检查是否可以开新仓
    bool can_open_long = (long_positions < Max_Long_Positions);
    bool can_open_short = (short_positions < Max_Short_Positions);

    // 执行交易逻辑
    if(can_open_long || can_open_short) {
        ExecuteTrading(long_positions, short_positions, can_open_long, can_open_short);
    }

    // 更新交易统计
    UpdateTradeStatistics();

    // 更新仪表盘
    UpdateDashboard();
}

//+------------------------------------------------------------------+
//| 获取指标数据                                                      |
//+------------------------------------------------------------------+
bool GetIndicatorData()
{
    // 复制指标数据
    if(CopyBuffer(ema20_handle, 0, 0, 25, ema20) <= 0) return false;
    if(CopyBuffer(ema50_handle, 0, 0, 25, ema50) <= 0) return false;
    if(CopyBuffer(atr_handle, 0, 0, 25, atr_values) <= 0) return false;

    // 复制均线过滤数据
    if(Enable_MA_Filter) {
        if(CopyBuffer(ma_filter_handle, 0, 0, 5, ma_filter_values) <= 0) return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 计算交易信号                                                      |
//+------------------------------------------------------------------+
void CalculateSignals()
{
    // 获取价格数据
    double high[], low[], close[], open[];
    long volume[];

    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(open, true);
    ArraySetAsSeries(volume, true);

    CopyHigh(Symbol(), PERIOD_M3, 0, 25, high);
    CopyLow(Symbol(), PERIOD_M3, 0, 25, low);
    CopyClose(Symbol(), PERIOD_M3, 0, 25, close);
    CopyOpen(Symbol(), PERIOD_M3, 0, 25, open);
    CopyTickVolume(Symbol(), PERIOD_M3, 0, 25, volume);

    // ================================
    // 趋势判断
    // ================================
    double ema20_slope = ema20[0] - ema20[1];
    double ema50_slope = ema50[0] - ema50[1];
    breakout_monitor.uptrend = (ema20[0] > ema50[0] && ema20_slope > 0 && ema50_slope > 0);
    breakout_monitor.downtrend = (ema20[0] < ema50[0] && ema20_slope < 0 && ema50_slope < 0);

    // ================================
    // 多头突破信号计算
    // ================================
    // 计算前20根K线的最高点（不包括当前K线）
    breakout_monitor.highest_high = high[1];
    for(int i = 2; i <= 20; i++) {
        if(high[i] > breakout_monitor.highest_high) {
            breakout_monitor.highest_high = high[i];
        }
    }

    // ================================
    // 空头突破信号计算
    // ================================
    // 计算前20根K线的最低点（不包括当前K线）
    breakout_monitor.lowest_low = low[1];
    for(int i = 2; i <= 20; i++) {
        if(low[i] < breakout_monitor.lowest_low) {
            breakout_monitor.lowest_low = low[i];
        }
    }

    // 成交量均线（修正：计算最近20根K线的平均，但不包括当前K线）
    double vol_ma = 0;
    for(int i = 1; i <= Volume_MA_Period; i++) {
        vol_ma += (double)volume[i];
    }
    vol_ma = vol_ma / Volume_MA_Period;

    // 多头突破条件
    breakout_monitor.long_price_above_highest = (close[0] > breakout_monitor.highest_high);
    breakout_monitor.long_volume_above_ma = ((double)volume[0] > vol_ma);
    breakout_monitor.long_volume_ratio = vol_ma > 0 ? (double)volume[0] / vol_ma : 0;

    bool long_breakout = breakout_monitor.long_price_above_highest && breakout_monitor.long_volume_above_ma;
    breakout_monitor.long_breakout_signal = breakout_monitor.uptrend && long_breakout;

    // 空头突破条件
    breakout_monitor.short_price_below_lowest = (close[0] < breakout_monitor.lowest_low);
    breakout_monitor.short_volume_above_ma = ((double)volume[0] > vol_ma);
    breakout_monitor.short_volume_ratio = vol_ma > 0 ? (double)volume[0] / vol_ma : 0;

    bool short_breakout = breakout_monitor.short_price_below_lowest && breakout_monitor.short_volume_above_ma;
    breakout_monitor.short_breakout_signal = breakout_monitor.downtrend && short_breakout;

    // 调试信息
    if(Debug_Mode) {
        static datetime last_debug_time = 0;
        datetime current_time = TimeCurrent();
        if(current_time - last_debug_time >= 60) {  // 每分钟输出一次
            Print("===== 信号状态 =====");
            Print("当前价格: ", close[0], " 前高: ", breakout_monitor.highest_high, " 前低: ", breakout_monitor.lowest_low);
            Print("EMA20: ", ema20[0], " EMA50: ", ema50[0]);
            Print("多头突破信号: ", breakout_monitor.long_breakout_signal,
                  " (上升趋势:", breakout_monitor.uptrend,
                  " 价格突破:", breakout_monitor.long_price_above_highest,
                  " 成交量:", breakout_monitor.long_volume_above_ma, ")");
            Print("空头突破信号: ", breakout_monitor.short_breakout_signal,
                  " (下降趋势:", breakout_monitor.downtrend,
                  " 价格突破:", breakout_monitor.short_price_below_lowest,
                  " 成交量:", breakout_monitor.short_volume_above_ma, ")");
            last_debug_time = current_time;
        }
    }
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                      |
//+------------------------------------------------------------------+
double GetPositionSize(double stop_price, int current_positions)
{
    double risk_amount = AccountInfoDouble(ACCOUNT_EQUITY) * Risk_Percent / 100.0;
    double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double stop_distance = MathAbs(current_price - stop_price);

    if(stop_distance <= 0) {
        Print("错误：止损距离为0");
        return 0;
    }

    double tick_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

    double value_per_point = tick_value / tick_size;
    double base_position_size = risk_amount / (stop_distance * value_per_point);

    // 根据持仓数量调整手数：第2单减半，第3单再减半，以此类推
    double position_size = base_position_size;
    for(int i = 0; i < current_positions; i++) {
        position_size = position_size / 2.0;
    }

    // 标准化手数
    double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

    position_size = MathMax(min_lot, MathMin(max_lot,
                   MathFloor(position_size / lot_step) * lot_step));

    if(Debug_Mode) {
        Print("仓位计算 - 当前持仓数:", current_positions,
              " 基础手数:", base_position_size,
              " 调整后手数:", position_size,
              " 风险金额:", risk_amount,
              " 止损距离:", stop_distance);
    }

    return position_size;
}

//+------------------------------------------------------------------+
//| 执行交易逻辑                                                      |
//+------------------------------------------------------------------+
void ExecuteTrading(int long_positions, int short_positions, bool can_open_long, bool can_open_short)
{
    datetime current_time = TimeCurrent();
    double current_ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double current_bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);

    double high[], low[], close[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);

    CopyHigh(Symbol(), PERIOD_M3, 0, 10, high);
    CopyLow(Symbol(), PERIOD_M3, 0, 10, low);
    CopyClose(Symbol(), PERIOD_M3, 0, 10, close);

    // ================================
    // 多头突破信号
    // ================================
    if(can_open_long && breakout_monitor.long_breakout_signal) {
        // 检查均线过滤条件
        bool ma_filter_ok = true;
        if(Enable_MA_Filter) {
            ma_filter_ok = (current_ask > ma_filter_values[0]);
            if(Debug_Mode) {
                Print("多头均线过滤检查 - 当前价格:", current_ask,
                      " 过滤均线:", ma_filter_values[0],
                      " 结果:", ma_filter_ok ? "通过" : "不通过");
            }
        }

        if(ma_filter_ok) {
            // 检查是否是新信号（避免重复开单）
            if(current_time - last_long_breakout_signal_time > 180) { // 3分钟冷却期
                // 计算止损价格（前5根K线最低点）
                double long_stop = low[1];
                for(int i = 2; i <= 5; i++) {
                    if(low[i] < long_stop) long_stop = low[i];
                }

                double long_pos_size = GetPositionSize(long_stop, long_positions);

                if(long_pos_size > 0) {
                    // 计算止盈价格
                    double stop_distance = current_ask - long_stop;
                    double take_profit = current_ask + stop_distance * Profit_Ratio;

                    // 开仓（如果启用追踪止损，则不设置固定止盈）
                    double final_take_profit = Enable_Trailing_Stop ? 0 : take_profit;

                    if(trade.Buy(long_pos_size, Symbol(), current_ask, long_stop, final_take_profit, "LONG_BREAKOUT")) {
                        Print("多头突破开仓成功 - 第", (long_positions + 1), "单",
                              " 手数:", long_pos_size,
                              " 当前多头持仓数:", long_positions,
                              " 入场价:", current_ask,
                              " 止损:", long_stop,
                              " 止盈:", final_take_profit > 0 ? DoubleToString(final_take_profit) : "追踪止损");
                        last_long_breakout_signal_time = current_time;
                    } else {
                        Print("多头突破开仓失败 - 错误:", GetLastError());
                    }
                }
            }
        }
    }

    // ================================
    // 空头突破信号
    // ================================
    if(can_open_short && breakout_monitor.short_breakout_signal) {
        // 检查均线过滤条件
        bool ma_filter_ok = true;
        if(Enable_MA_Filter) {
            ma_filter_ok = (current_bid < ma_filter_values[0]);
            if(Debug_Mode) {
                Print("空头均线过滤检查 - 当前价格:", current_bid,
                      " 过滤均线:", ma_filter_values[0],
                      " 结果:", ma_filter_ok ? "通过" : "不通过");
            }
        }

        if(ma_filter_ok) {
            // 检查是否是新信号（避免重复开单）
            if(current_time - last_short_breakout_signal_time > 180) { // 3分钟冷却期
                // 计算止损价格（前5根K线最高点）
                double short_stop = high[1];
                for(int i = 2; i <= 5; i++) {
                    if(high[i] > short_stop) short_stop = high[i];
                }

                double short_pos_size = GetPositionSize(short_stop, short_positions);

                if(short_pos_size > 0) {
                    // 计算止盈价格
                    double stop_distance = short_stop - current_bid;
                    double take_profit = current_bid - stop_distance * Profit_Ratio;

                    // 开仓（如果启用追踪止损，则不设置固定止盈）
                    double final_take_profit = Enable_Trailing_Stop ? 0 : take_profit;

                    if(trade.Sell(short_pos_size, Symbol(), current_bid, short_stop, final_take_profit, "SHORT_BREAKOUT")) {
                        Print("空头突破开仓成功 - 第", (short_positions + 1), "单",
                              " 手数:", short_pos_size,
                              " 当前空头持仓数:", short_positions,
                              " 入场价:", current_bid,
                              " 止损:", short_stop,
                              " 止盈:", final_take_profit > 0 ? DoubleToString(final_take_profit) : "追踪止损");
                        last_short_breakout_signal_time = current_time;
                    } else {
                        Print("空头突破开仓失败 - 错误:", GetLastError());
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 计算多头持仓数量                                                   |
//+------------------------------------------------------------------+
int CountLongPositions()
{
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionSelectByTicket(PositionGetTicket(i))) {
            if(PositionGetString(POSITION_SYMBOL) == Symbol() &&
               PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
               PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) {
                count++;
            }
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| 计算空头持仓数量                                                   |
//+------------------------------------------------------------------+
int CountShortPositions()
{
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionSelectByTicket(PositionGetTicket(i))) {
            if(PositionGetString(POSITION_SYMBOL) == Symbol() &&
               PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
               PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL) {
                count++;
            }
        }
    }
    return count;
}
